.container336456 {
  padding-bottom: 160rpx;
}

.swiper-clz {
  padding: 8px 0;
  border-bottom-left-radius: 12rpx;
  overflow: hidden;
  border-top-left-radius: 12rpx;
  border-top-right-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}

.swiper-scale {
  transform: scaleY(0.9);
  margin: 0 20rpx;
}

.swiper-title {
  background-color: rgba(0, 0, 0, 0.281);
}

.button-button-clz {
  font-size: 28rpx !important;
  margin: 6rpx !important;
  padding: 48rpx !important;
}

.flex16-clz {
  margin: 30rpx;
  border-radius: 24rpx;
  overflow: hidden;
  width: calc(100% - 30rpx - 30rpx) !important;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  padding: 30rpx;
}

.flex17-clz {
  padding: 0;
}

.text13-clz {
  color: #333;
  font-weight: bold;
  font-size: 30rpx !important;
}

.flex30-clz {
  color: rgba(255, 67, 143, 1);
  font-size: 22rpx !important;
  padding: 10rpx 20rpx;
  border-radius: 32rpx;
  border: 1px solid rgba(255, 67, 143, 1);
}

.text32-clz {
  text-align: right;
}

.flex6-clz {
  margin: 10rpx 0rpx 10rpx -30rpx;
  width: calc(100% + 30rpx - 0rpx) !important;
}

.flex46-clz {
  padding: 30rpx;
  border-radius: 24rpx;
  margin: 15rpx 0rpx 15rpx 30rpx;
  background-color: rgba(255, 192, 218, 0.3);
  overflow: hidden;
  width: calc(70% - 20rpx - 0rpx) !important;
  transition: all 0.3s ease;
  cursor: pointer;
}

.flex46-clz:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.image7-clz {
  border-radius: 24rpx;
  overflow: hidden;
}

.image7-size {
  height: 160rpx !important;
  width: 160rpx !important;
}

.flex48-clz {
  padding-top: 0rpx;
  flex: 1;
  padding-left: 20rpx;
  padding-bottom: 0rpx;
  padding-right: 0rpx;
}

.text23-clz {
  font-size: 30rpx !important;
  font-weight: bold;
  color: #333;
}

.flex49-clz {
  margin: 6rpx 0;
  width: calc(100% - 0rpx - 0rpx) !important;
  flex-direction: column;
}

.text24-pet-ly {
  line-height: 50rpx;
  font-size: 24rpx !important;
  color: #666;
}

.lastServiceInfo {
  margin-top: 8rpx;
}

.lastServiceTime {
  font-size: 22rpx;
  color: #999;
  line-height: 40rpx;
}

.container336456 .diygw-grid.col-4 {
  padding: 20rpx 20rpx 0;
}

.container336456 .diygw-avatar {
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1);
  border-radius: 24rpx;
  padding: 16rpx;
  box-sizing: content-box;
}

.container336456 .diygw-grid-title {
  line-height: 70rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.container336456 .diygw-grid-subtitle {
  font-size: 20rpx;
  color: rgba(153, 153, 153, 1);
  display: block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.container336456 .tabs {
  margin: 0 30rpx;
  width: calc(100vw - 60rpx) !important;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
  border-radius: 24rpx;
}

.container336456 .tabs-item-title {
  color: #999 !important;
}

.container336456 .tabs-item-title.cur {
  color: #333 !important;
}

.container336456 .diygw-tabs.small-border .diygw-tab-item.cur::after {
  background-color: #FF4391;
  height: 8rpx;
  border-radius: 8rpx;
}

.container336456 .tabs .tabContainer {
  padding: 30rpx;
}

.tab-map {
  height: 100rpx;
  border-radius: 24rpx;
}

.flex-tab-title {
  height: 100%;
  padding: 0 24rpx;
  color: #666;
}

.address-area-ly {
  line-height: 100rpx;
  font-size: 30rpx;
}

.city-ly {
  font-weight: bold;
}

.address-area-ly .icon1 {
  font-size: 32rpx;
  color: #d3d3d3;
}

.button-group-ly button:nth-child(1) {
  margin: 0 15rpx 0 0 !important;
  box-shadow: 0px 3px 0px 0px rgba(255, 67, 145, 0.3);
}

.button-group-ly button:nth-child(2) {
  margin: 0 0 0 15rpx !important;
  box-shadow: 0px 3px 0px 0px rgba(47, 131, 255, 0.2);
}

.image-wrapper-ly {
  padding: 0 30rpx;
}

.cityOverflowOmission {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90rpx;
}

.locationOverflowOmission {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 400rpx;
}

.diy-icon-locationfill {
  margin-left: 30rpx;
}