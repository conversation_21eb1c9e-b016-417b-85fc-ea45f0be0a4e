<!-- pages/reviewDetail/reviewDetail.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">我的评价</text>
  </view>

  <!-- 订单信息 -->
  <view class="order-info" wx:if="{{orderDetail}}">
    <view class="order-content">
      <image class="service-image" src="{{orderDetail.orderDetails[0].service.logo}}" mode="aspectFill"></image>
      <view class="service-info">
        <text class="service-name">{{orderDetail.orderDetails[0].service.serviceName}}</text>
        <text class="service-price">¥{{orderDetail.totalFee}}</text>
        <text class="order-time">{{orderDetail.createdAt}}</text>
      </view>
    </view>
  </view>

  <!-- 服务照片区域 -->
  <!-- <view wx:if="{{servicePhotos && ((servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) || (servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0))}}" class="service-photos">
    服务前照片
    <view wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务前照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.beforePhotoTime}}">{{servicePhotos.beforePhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.beforePhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="before"
          ></image>
        </view>
      </view>
    </view>

    服务后照片
    <view wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}" class="photo-section">
      <view class="section-title">
        <text class="title-text">服务后照片</text>
        <text class="photo-time" wx:if="{{servicePhotos.afterPhotoTime}}">{{servicePhotos.afterPhotoTime}}</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{servicePhotos.afterPhotos}}" wx:key="index" class="photo-item">
          <image
            src="{{item}}"
            class="photo-preview"
            mode="aspectFill"
            bindtap="previewServicePhoto"
            data-url="{{item}}"
            data-type="after"
          ></image>
        </view>
      </view>
    </view>
  </view> -->

  <!-- 评价内容 -->
  <view class="review-content" wx:if="{{reviewData}}">
    <!-- 评分显示 -->
    <view class="rating-section">
      <view class="section-title">
        <text>服务评分</text>
      </view>
      <view class="rating-display">
        <view class="stars">
          <view wx:for="{{[1,2,3,4,5]}}" wx:key="index" class="star {{reviewData.rating >= item ? 'active' : ''}}">
            <text class="star-icon">★</text>
          </view>
        </view>
        <text class="rating-text">{{ratingTexts[reviewData.rating-1]}}</text>
        <text class="rating-score">{{reviewData.rating}}.0</text>
      </view>
    </view>

    <!-- 评价文字 -->
    <view class="comment-section">
      <view class="section-title">
        <text>评价内容</text>
      </view>
      <view class="comment-content">
        <text class="comment-text">{{reviewData.comment}}</text>
      </view>
    </view>

    <!-- 评价图片 -->
    <view class="photo-section" wx:if="{{reviewData.photoURLs && reviewData.photoURLs.length > 0}}">
      <view class="section-title">
        <text>评价图片</text>
      </view>
      <view class="photo-grid">
        <view wx:for="{{reviewData.photoURLs}}" wx:key="index" class="photo-item">
          <image src="{{item}}" class="photo-preview" mode="aspectFill" bindtap="previewImage" data-url="{{item}}"></image>
        </view>
      </view>
    </view>

    <!-- 评价时间 -->
    <view class="review-time">
      <text class="time-text">评价时间：{{reviewData.createdAt}}</text>
    </view>
  </view>

  <!-- 无评价提示 -->
  <view class="no-review" wx:if="{{!reviewData && !loading}}">
    <view class="no-review-icon">📝</view>
    <text class="no-review-text">暂无评价内容</text>
    <text class="no-review-desc">该订单还没有评价</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
