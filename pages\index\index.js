import dictionaryApi from '../../api/modules/dictionary.js';
import locationApi from '../../api/modules/location';
import userApi from '../../api/modules/user';
import siteinfo from '../../siteinfo.js';
import utils from '../utils/util';

Page({
  data: {
    //用户全局信息，page-extend.js会自动注入
    userInfo: null,
    siteinfo,
    showTicketModal: false,
    currentLocation: '未知位置',
    currentCity: '未知位置',
    vehicles: [], // 附近车辆列表
    pets: [],
    swiperIndex: 0,
    pets: [],
    tabsDatas: [],
    tabsName: null,
    tabsIndex: null,
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '该功能正在开发中，我们正努力为您带来更好的体验！',
  },
  onLoad(option) {
    if (option.aUserId || option.shareCode) {
      console.log('推广信息：', option);
      // 存储推广信息
      wx.setStorageSync('promoterInfo', {
        aUserId: option.aUserId,
        shareCode: option.shareCode,
        shareTime: option.shareTime,
      });
    }
    dictionaryApi.list().then(res => {
      // 加载字典数据
      console.log('字典数据：');
      console.log(res);
    });
    // 查询附近车辆
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    this.intervalId = setInterval(() => {
      this.findNearbyVehicles();
    }, 1000 * 30);
  },

  // 启用分享
  onShareAppMessage: function () {
    return {
      title: '贝宠约洗，足不出户享精致洗护～',
      path: `/pages/index/index?aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share.png',
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },
  // 启用分享-朋友圈
  onShareTimeline: function () {
    return {
      title: '「忙到没空送洗？上门服务解忧！」贝宠约洗，专业到家超省心～',
      imageUrl: 'https://xian7.zos.ctyun.cn/pet/static/share1.png',
      query: `aUserId=${this.data?.userInfo?.id || ''}&shareCode=${
        this.data?.userInfo?.promotionCode || ''
      }&shareTime=${Date.now()}`,
      // imageUrl: '/assets/share.png' // 这里填写图片的相对路径或绝对路径
    };
  },

  onShow() {
    // TODO 后期放开
    // this.getLocation();
    this.getPets();
    this.getCategories();
  },
  onUnload() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  getCategories() {
    dictionaryApi.list('服务类型').then(list => {
      const tabsDatas = list.map(item => ({
        text: '约' + item.name,
        name: item.name,
        code: item.code,
      }));
      // 设置数据并默认选中第一项
      this.setData({
        tabsDatas,
        tabsIndex: tabsDatas[0]?.code || '',
        tabsName: tabsDatas[0]?.name || '',
      });
    });
  },
  changeSwiper(evt) {
    let swiperIndex = evt.detail.current;
    this.setData({
      swiperIndex,
    });
  },
  changeTabs(evt) {
    let { index, name } = evt.currentTarget.dataset;
    if (index == this.data.tabsIndex) return;
    this.setData({
      tabsIndex: index,
      tabsName: name || '',
    });
  },
  clickTmapMarker(evt) {
    let marker = this.data.tmap.markers[evt.detail.markerId];
    if (marker && marker.action) {
      this.navigateTo(marker.action);
    }
  },
  redirect(evt) {
    let { type, subtype } = evt.currentTarget.dataset;
    let url;
    const thiz = this;
    switch (type) {
      case 'system':
        url = '/pages/service/index?fromNav=true';
        break;
      case 'nursor':
        url = '/pages/service/nursor/index';
        break;
      case 'vip':
        url = '/pages/mine/rightsCenter/rightsCenter';
        break;
      default:
        break;
    }
    if (url) {
      thiz.navigateTo({
        type: 'page',
        url,
        actionType: type,
        data: subtype,
      });
    } else {
      // 显示自定义模态框
      this.setData({
        showModal: true,
      });
    }
  },

  getLocation() {
    // TODO 后期放开
    return;
    this.setData({
      currentCity: '定位中...',
      currentLocation: '定位中...',
    });
    wx.getSetting({
      success: res => {
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => this._getActualLocation(),
            fail: () =>
              wx.showToast({
                title: '请授权位置信息',
                icon: 'none',
              }),
          });
        } else {
          this._getActualLocation();
        }
      },
    });
  },

  _getActualLocation() {
    // TODO 后期放开
    return;
    wx.getLocation({
      altitude: true,
      highAccuracyExpireTime: 0,
      isHighAccuracy: true,
      type: 'gcj02',
      success: result => {
        this.setData({
          latitude: result.latitude,
          longitude: result.longitude,
        });
        this.findNearbyVehicles();
        // 调用高德逆地理编码API
        this._getAddressFromAMap(result.latitude, result.longitude);
      },
      fail: res => {
        this.setData({
          currentCity: '定位失败',
          currentLocation: '定位失败',
        });
        wx.showToast({
          title: '获取位置失败',
          icon: 'error',
        });
      },
    });
  },

  // 新增方法：调用高德逆地理编码API
  _getAddressFromAMap(latitude, longitude) {
    wx.request({
      url: 'https://restapi.amap.com/v3/geocode/regeo',
      data: {
        key: siteinfo.constant.AMapKey,
        location: `${longitude},${latitude}`,
        radius: 1000,
        extensions: 'base',
      },
      success: res => {
        if (res.data && res.data.status === '1') {
          const { city, district, township, streetNumber } = res.data.regeocode.addressComponent;
          const { street, number } = streetNumber || {};
          const address = district + township + street + number;
          this.setData({
            currentCity: city,
            currentLocation: address,
          });
        }
      },
      fail: err => {
        console.error('地理编码失败', err);
        wx.showToast({
          title: '解析地理编码失败' + err,
          icon: 'error',
        });
        this.setData({
          currentCity: '位置解析失败',
          currentLocation: '位置解析失败',
        });
      },
    });
  },

  // 查找附近车辆
  findNearbyVehicles() {
    const { latitude, longitude } = this.data;
    if (!latitude || !longitude) {
      return;
    }
    locationApi
      .findNearbyVehicles({
        lat: latitude,
        lng: longitude,
      })
      .then(list => {
        // console.log("findNearbyVehicles: ", list);
        this.setData({
          vehicles: list,
        });
      });
  },

  addPet() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.navigateTo({
        url: '/pages/login/index',
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/service/pet/index',
    });
  },

  async getPets() {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.setData({
        pets: [],
      });
      return;
    }

    try {
      const data = await userApi.getPets(userInfo.id);

      // 并行获取每个宠物的最后洗护时间
      const formattedData = await Promise.all(data.map(async item => {
        // 格式化年龄
        if (typeof item.bri === 'number' && !isNaN(item.bri)) {
          item.formattedBri = utils.formatAge(item.bri);
        } else {
          item.formattedBri = '无效年龄';
        }

        // 获取最后洗护时间
        try {
          const lastServiceData = await userApi.getLastServiceTime(userInfo.id, item.id);
          item.lastServiceTime = lastServiceData.lastServiceTime;
          item.formattedLastServiceTime = utils.formatLastServiceTime(lastServiceData.lastServiceTime);
        } catch (error) {
          console.log(`获取宠物${item.name}最后洗护时间失败:`, error);
          item.lastServiceTime = null;
          item.formattedLastServiceTime = '暂无洗护记录';
        }

        return item;
      }));

      this.setData({
        pets: formattedData,
      });
    } catch (err) {
      console.log(err);
    }
  },
  navigationMap() {
    wx.navigateTo({
      url: '/pages/map/map',
    });
  },

  /**
   * 查看宠物详情
   */
  viewPetDetail(event) {
    const item = event.currentTarget.dataset.item;
    console.log('首页点击宠物详情:', item);

    if (!item || !item.id) {
      wx.showToast({
        title: '宠物信息错误',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/service/pet/detail?petId=${item.id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
});
