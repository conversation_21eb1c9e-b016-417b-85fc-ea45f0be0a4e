import orderApi from '../../../api/modules/order.js';
import payApi from '../../../api/modules/pay';
import utils from '../../utils/util';
import WeMessage from '../../../common/WeMessage.js';
import Session from '../../../common/Session.js';
import { OrderStatus } from '../../../common/constant.js';
import additionalServiceApi from '../../../api/modules/additionalService.js';

Page({
  data: {
    userInfo: null,
    orderDetail: {}, // 订单
    servicePhotos: null, // 服务照片
    additionalServices: [], // 追加服务列表
    showMoreActions: false,
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '支付功能稍后上线！',
    modalButtons: [],
    clickEvent: () => {
      // 返回上一页
      wx.navigateBack({
        delta: 1,
      });
    },

    // 追加服务支付相关
    paying: false,
    currentPayService: null, // 当前要支付的追加服务

    // 追加服务状态映射
    additionalServiceStatusMap: {
      pending_confirm: {
        text: '待确认',
        color: '#ff9500',
        desc: '等待员工确认',
        actions: ['view', 'delete']
      },
      confirmed: {
        text: '已确认',
        color: '#007aff',
        desc: '请尽快完成支付',
        actions: ['pay', 'view', 'delete']
      },
      paid: {
        text: '已付款',
        color: '#34c759',
        desc: '服务进行中',
        actions: ['view']
      },
      rejected: {
        text: '已拒绝',
        color: '#ff3b30',
        desc: '申请被拒绝',
        actions: ['view', 'delete']
      }
    },
  },
  onLoad(options) {
    const { data } = options;
    const userInfo = Session.getUser();

    this.setData({ userInfo });

    // 加载初始订单数据
    this.loadOrders(data);
  },
  onShow() {
    // 如果订单状态是服务中，刷新追加服务列表
    const { orderDetail } = this.data;
    if (orderDetail && orderDetail.status === OrderStatus.服务中 && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      // 添加小延迟确保从申请页面返回时能获取到最新数据
      setTimeout(() => {
        this.loadAdditionalServices(orderDetail.orderDetails[0].id);
      }, 100);
    }
  },
  // 加载订单数据
  loadOrders(data) {
    wx.showLoading({
      title: '加载中',
    });

    const mockOrder = JSON.parse(data);
    const { serviceTime, orderTime, createdAt, ...rest } = mockOrder;
    const orderDetail = {
      ...rest,
      serviceTime: serviceTime ? utils.formatNormalDate(serviceTime) : null,
      // 下单时间
      orderTime: orderTime ? utils.formatNormalDate(orderTime) : null,
      // createdAt: createdAt ? utils.formatNormalDate(createdAt) : null,
    };

    this.setData({
      orderDetail,
    });

    // 如果订单状态是服务中、已完成或已评价，加载服务照片
    if (orderDetail.status === OrderStatus.服务中 ||
        orderDetail.status === OrderStatus.已完成 ||
        orderDetail.status === OrderStatus.已评价) {
      this.loadServicePhotos(orderDetail.id);
    }

    // 如果订单状态是服务中，加载追加服务
    if (orderDetail.status === OrderStatus.服务中 && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      this.loadAdditionalServices(orderDetail.orderDetails[0].id);
    }

    wx.hideLoading();
  },
  // 删除订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消此订单吗？',
      success: res => {
        if (res.confirm) {
          const userInfo = this.data.userInfo;
          orderApi.cancel(userInfo.id, orderId).then(res => {
            if (res) {
              wx.showToast({
                title: '取消成功',
                icon: 'success',
              });
              wx.navigateBack({
                delta: 1,
              });
            }
          });
        }
      },
    });
  },
  // 切换更多操作弹窗
  toggleOrderActions(e) {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 支付订单
  payOrder(e) {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.navigateTo({
        type: 'tip',
        tip: '获取订单信息失败，请重新登录',
      });
      return;
    }
    const sn = e.currentTarget.dataset.sn;
    const order = this.data.orderDetail;

    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none',
      });
      return;
    }

    if (Number(order.amount) === 0) {
      wx.showLoading({
        title: '处理中',
      });
      // 使用统一的0元订单处理方法
      orderApi.handleZeroAmountOrder(userInfo.id, sn).then(res => {
        wx.hideLoading();
        if (res.success) {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('6----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        } else {
          wx.showToast({
            title: res.error || '处理失败，请稍后重试',
            icon: 'none',
          });
        }
      });
      return;
    }

    // 支付
    payApi.doPay({
      sn,
      onOk: () => {
        // 支付成功后，调用支付成功接口更新订单状态
        orderApi.pay(userInfo.id, sn).then(res => {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('7----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        });
      },
      onCancel: () => {
        wx.showToast({
          title: '取消支付',
          icon: 'none',
        });
      },
      onError: () => {
        wx.showToast({
          title: '支付失败',
          icon: 'none',
        });
      },
      complete: () => {},
    });
  },
  /**
   * 处理支付成功后的弹窗 - 订单详情页面
   * @param {string} openId 用户openId
   * @param {string} sn 订单编号
   */
  handlePaymentSuccessModal(openId, sn) {
    const weMessage = new WeMessage(openId, sn, OrderStatus.待接单);
    const modalConfig = weMessage.handlePaymentSuccess();
    if (modalConfig) {
      this.setData({
        showModal: true,
        modalTitle: modalConfig.modalConfig.title,
        modalContent: modalConfig.modalConfig.content,
        modalButtons: modalConfig.modalConfig.buttons,
      });
      this._weMessage = weMessage;
    }
  },
  // 处理订阅确认按钮点击
  handleModalConfirm(e) {
    if (this._weMessage) {
      this._weMessage.requestOrderConfirmationSubscribe();
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },
  // 处理订阅取消按钮点击
  handleModalCancel(e) {
    if (this._weMessage) {
      this._weMessage.recordUserChoice(false);
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },

  // 加载服务照片
  async loadServicePhotos(orderId) {
    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        // 格式化时间
        const formattedPhotos = {
          ...servicePhotos,
          beforePhotoTime: servicePhotos.beforePhotoTime ? utils.formatNormalDate(servicePhotos.beforePhotoTime) : null,
          afterPhotoTime: servicePhotos.afterPhotoTime ? utils.formatNormalDate(servicePhotos.afterPhotoTime) : null,
        };
        this.setData({ servicePhotos: formattedPhotos });
      }
    } catch (error) {
      console.error('加载服务照片失败:', error);
      // 不显示错误提示，因为没有照片是正常情况
    }
  },

  // 预览服务照片
  previewServicePhoto(e) {
    const { url, type } = e.currentTarget.dataset;
    const { servicePhotos } = this.data;

    if (!servicePhotos || !url || !type) return;

    // 根据类型获取对应的照片数组
    const photos = type === 'before' ? servicePhotos.beforePhotos : servicePhotos.afterPhotos;

    if (!photos || photos.length === 0) return;

    wx.previewImage({
      current: url,
      urls: photos
    });
  },

  // 申请追加服务
  applyAdditionalService(e) {
    const { orderDetailId } = e.currentTarget.dataset;
    const { orderDetail } = this.data;

    if (!orderDetail || orderDetail.status !== OrderStatus.服务中) {
      wx.showToast({
        title: '当前订单状态不支持申请追加服务',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/additionalService/apply/index?orderDetailId=${orderDetailId}`
    });
  },

  // 加载追加服务列表
  async loadAdditionalServices(orderDetailId) {
    try {
      const services = await additionalServiceApi.list(orderDetailId);

      // 格式化数据
      const formattedServices = (services || []).map(item => ({
        ...item,
        createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
        confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
        statusInfo: this.data.additionalServiceStatusMap[item.status] || {
          text: item.status,
          color: '#999',
          desc: '',
          actions: ['view']
        }
      }));

      this.setData({
        additionalServices: formattedServices
      });

    } catch (error) {
      console.error('加载追加服务列表失败:', error);
      // 不显示错误提示，因为没有追加服务是正常情况
      // 但设置空数组确保UI状态正确
      this.setData({
        additionalServices: []
      });
    }
  },

  // 查看追加服务详情
  viewAdditionalServiceDetail(e) {
    const { id } = e.currentTarget.dataset;
    const { orderDetail } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) return;

    const orderDetailId = orderDetail.orderDetails[0].id;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${id}`
    });
  },

  // 支付追加服务 - 直接在当前页面处理支付
  payAdditionalService(e) {
    const { id } = e.currentTarget.dataset;
    const { additionalServices } = this.data;

    // 找到要支付的服务
    const service = additionalServices.find(item => item.id === parseInt(id));
    if (!service) {
      wx.showToast({
        title: '服务信息不存在',
        icon: 'none',
      });
      return;
    }

    // 检查状态
    if (service.status !== 'confirmed') {
      wx.showToast({
        title: '当前状态不支持支付',
        icon: 'none',
      });
      return;
    }

    // 设置当前支付服务并显示确认模态框
    this.setData({
      currentPayService: service,
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${service.totalFee} 吗？`,
      modalButtons: [
        { text: '取消', type: 'cancel', event: 'handlePayModalCancel' },
        { text: '确认支付', type: 'primary', event: 'handlePayConfirm' },
      ],
    });
  },

  // 删除追加服务
  deleteAdditionalService(e) {
    const { id, name } = e.currentTarget.dataset;

    wx.showModal({
      title: '删除确认',
      content: `确定要删除追加服务"${name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteAdditionalService(id);
        }
      }
    });
  },

  // 执行删除追加服务
  async performDeleteAdditionalService(id) {
    try {
      wx.showLoading({ title: '删除中...' });

      const { orderDetail, userInfo } = this.data;
      if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
        throw new Error('订单详情不存在');
      }

      if (!userInfo || !userInfo.id) {
        throw new Error('用户信息不存在');
      }

      const orderDetailId = orderDetail.orderDetails[0].id;

      // 调用删除API，传递customerId
      await additionalServiceApi.delete(orderDetailId, id, userInfo.id);

      // 重新加载追加服务列表
      await this.loadAdditionalServices(orderDetailId);

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('删除追加服务失败:', error);

      // 根据错误类型显示不同的提示信息
      let errorMessage = '删除失败';
      if (error.message && error.message.includes('待确认')) {
        errorMessage = '只能删除待确认状态的申请';
      } else if (error.message && error.message.includes('权限')) {
        errorMessage = '无权限删除此申请';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 处理追加服务支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });

    // 先同步支付状态，检查是否已经支付
    const { currentPayService } = this.data;
    if (currentPayService) {
      wx.showLoading({ title: '检查支付状态...' });

      const syncResult = await this.syncAdditionalServicePaymentStatus(currentPayService.id);
      wx.hideLoading();

      if (syncResult) {
        // 同步成功，检查同步结果
        const { additionalServices } = this.data;
        const updatedService = additionalServices.find(item => item.id === currentPayService.id);

        if (updatedService && updatedService.status === 'paid') {
          // 已经是已支付状态，直接显示成功并刷新
          this.setData({ currentPayService: null });
          this.showAdditionalServicePaymentSuccessModal();
          return;
        }
      }
    }

    // 如果未支付或同步失败，继续正常支付流程
    await this.processAdditionalServicePay();
  },

  /**
   * 处理追加服务支付
   */
  async processAdditionalServicePay() {
    const { userInfo, orderDetail, currentPayService } = this.data;

    if (!currentPayService || this.data.paying) return;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none',
      });
      return;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      this.setData({ paying: true });

      // 处理0元订单
      if (Number(currentPayService.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });
        const result = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
        wx.hideLoading();

        if (result) {
          // 0元订单支付成功后刷新数据并显示成功
          await this.refreshOrderData();
          this.showAdditionalServicePaymentSuccessModal();
        } else {
          wx.showToast({ title: '支付失败，请重试', icon: 'none' });
        }
        return;
      }

      // 正常支付流程
      wx.showLoading({ title: '发起支付...' });
      const payResult = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
      wx.hideLoading();

      if (payResult && payResult.orderSn) {
        // 调用微信支付
        const _this = this;
        payApi.doPay({
          sn: payResult.orderSn,
          onOk: async () => {
            // 支付成功后刷新数据并显示成功
            await _this.refreshOrderData();
            _this.showAdditionalServicePaymentSuccessModal();
          },
          onCancel: () => wx.showToast({ title: '取消支付', icon: 'none' }),
          onError: () => wx.showToast({ title: '支付失败', icon: 'none' }),
        });
      } else {
        wx.showToast({ title: '支付失败，请重试', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('追加服务支付失败:', error);
      wx.showToast({ title: '支付失败，请重试', icon: 'none' });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示追加服务支付成功模态框
   */
  showAdditionalServicePaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [{ text: '确定', type: 'primary', event: 'handleAdditionalServicePaymentSuccess' }],
    });
  },

  /**
   * 刷新订单数据 - 从服务器重新获取最新数据
   */
  async refreshOrderData() {
    const { orderDetail, userInfo } = this.data;
    if (orderDetail && orderDetail.id && userInfo && userInfo.id) {
      try {
        wx.showLoading({ title: '刷新中...' });

        // 从服务器重新获取订单详情
        const latestOrderDetail = await orderApi.getDetail(userInfo.id, orderDetail.id);

        if (latestOrderDetail) {
          // 使用最新的订单数据重新加载页面
          const orderData = JSON.stringify(latestOrderDetail);
          this.loadOrders(orderData);
        }

        wx.hideLoading();
      } catch (error) {
        wx.hideLoading();
        console.error('刷新订单数据失败:', error);
        // 刷新失败时，仍然重新加载追加服务列表
        if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
          this.loadAdditionalServices(orderDetail.orderDetails[0].id);
        }
      }
    }
  },

  /**
   * 同步追加服务支付状态
   * @param {number} serviceId 追加服务ID
   * @param {boolean} autoRefresh 是否自动刷新数据，默认true
   */
  async syncAdditionalServicePaymentStatus(serviceId, autoRefresh = true) {
    const { orderDetail, userInfo } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      console.error('订单详情不存在，无法同步支付状态');
      return false;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      console.log('开始同步追加服务支付状态...');
      const syncResult = await additionalServiceApi.syncPaymentStatus(orderDetailId, serviceId, userInfo.id);

      if (syncResult && syncResult.success) {
        console.log('支付状态同步成功:', syncResult.message);

        // 根据参数决定是否自动刷新订单数据
        if (autoRefresh) {
          await this.refreshOrderData();
        }

        return syncResult;
      } else {
        console.log('支付状态同步失败:', syncResult?.message || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('同步支付状态异常:', error);
      return false;
    }
  },

  /**
   * 处理追加服务支付成功
   */
  async handleAdditionalServicePaymentSuccess() {
    this.setData({
      showModal: false,
      currentPayService: null
    });

    // 点击确定按钮后刷新详情页面
    await this.refreshOrderData();
  },

  /**
   * 处理支付模态框取消
   */
  handlePayModalCancel() {
    this.setData({
      showModal: false,
      currentPayService: null
    });
  },
});
